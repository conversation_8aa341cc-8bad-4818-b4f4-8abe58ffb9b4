#!/usr/bin/python
# -*- coding: utf-8 -*-

"""
多模态测试用例生成演示脚本
展示如何使用多模态功能生成测试用例
"""

import asyncio
from PIL import Image
import io
import base64

def create_demo_image():
    """创建演示图片"""
    # 创建一个简单的登录界面模拟图片
    img = Image.new('RGB', (400, 300), color='white')
    
    # 这里可以添加更复杂的图片绘制逻辑
    # 为了演示，我们创建一个简单的彩色块
    pixels = img.load()
    
    # 绘制一个简单的界面布局
    for x in range(50, 350):
        for y in range(50, 100):
            pixels[x, y] = (200, 200, 255)  # 标题区域
    
    for x in range(50, 350):
        for y in range(120, 160):
            pixels[x, y] = (255, 255, 200)  # 用户名输入框
    
    for x in range(50, 350):
        for y in range(180, 220):
            pixels[x, y] = (255, 255, 200)  # 密码输入框
    
    for x in range(150, 250):
        for y in range(240, 270):
            pixels[x, y] = (200, 255, 200)  # 登录按钮
    
    return img

def demo_text_processing():
    """演示文本处理功能"""
    print("📄 演示文本处理功能")
    print("-" * 30)
    
    try:
        from file_processor import FileProcessor
        processor = FileProcessor()
        
        # 模拟需求文档内容
        demo_text = """
用户登录功能需求文档

1. 功能概述
   用户可以通过用户名和密码登录系统

2. 功能要求
   - 用户名长度3-20个字符
   - 密码长度8-20个字符，必须包含字母和数字
   - 登录失败3次后锁定账户30分钟
   - 支持记住密码功能

3. 界面要求
   - 用户名输入框
   - 密码输入框
   - 登录按钮
   - 记住密码复选框
   - 忘记密码链接
"""
        
        print("原始文档内容:")
        print(demo_text[:200] + "...")
        
        # 这里可以添加实际的文档处理逻辑
        print("\n✅ 文档处理完成")
        return demo_text
        
    except Exception as e:
        print(f"❌ 文本处理失败: {e}")
        return ""

def demo_image_processing():
    """演示图片处理功能"""
    print("\n🖼️ 演示图片处理功能")
    print("-" * 30)
    
    try:
        from multimodal_message import image_to_base64
        
        # 创建演示图片
        demo_img = create_demo_image()
        print("✅ 创建演示图片成功")
        print(f"   图片尺寸: {demo_img.size}")
        
        # 转换为base64
        base64_str = image_to_base64(demo_img)
        print(f"✅ 图片转换成功，Base64长度: {len(base64_str)}")
        
        return [demo_img]
        
    except Exception as e:
        print(f"❌ 图片处理失败: {e}")
        return []

def demo_multimodal_message():
    """演示多模态消息创建"""
    print("\n📝 演示多模态消息创建")
    print("-" * 30)
    
    try:
        from multimodal_message import prepare_multimodal_request
        
        # 准备测试数据
        text_content = "用户登录功能测试"
        uploaded_text = demo_text_processing()
        images = demo_image_processing()
        
        # 创建多模态请求
        request = prepare_multimodal_request(
            text_content=text_content,
            images=images,
            uploaded_text=uploaded_text,
            test_priority="高",
            test_case_count=5
        )
        
        print("✅ 多模态请求创建成功")
        print(f"   模式: {'多模态' if request['is_multimodal'] else '文本'}")
        print(f"   图片数量: {request['image_count']}")
        print(f"   任务描述长度: {len(request['task'])}")
        
        # 显示任务描述的前200个字符
        print("\n任务描述预览:")
        print(request['task'][:300] + "...")
        
        return request
        
    except Exception as e:
        print(f"❌ 多模态消息创建失败: {e}")
        return None

async def demo_testcase_generation():
    """演示测试用例生成（模拟）"""
    print("\n🤖 演示测试用例生成")
    print("-" * 30)
    
    # 模拟生成的测试用例
    mock_testcases = """
# 用户登录功能测试用例

## 测试用例1：正常登录
**测试场景**: 用户使用正确的用户名和密码登录
**前置条件**: 用户账户存在且未被锁定
**测试步骤**:
1. 打开登录页面
2. 输入正确的用户名
3. 输入正确的密码
4. 点击登录按钮
**预期结果**: 登录成功，跳转到主页

## 测试用例2：用户名格式验证
**测试场景**: 输入不符合格式要求的用户名
**前置条件**: 无
**测试步骤**:
1. 打开登录页面
2. 输入长度小于3个字符的用户名
3. 输入正确的密码
4. 点击登录按钮
**预期结果**: 显示用户名格式错误提示

## 测试用例3：密码格式验证
**测试场景**: 输入不符合格式要求的密码
**前置条件**: 无
**测试步骤**:
1. 打开登录页面
2. 输入正确的用户名
3. 输入只包含字母的密码
4. 点击登录按钮
**预期结果**: 显示密码格式错误提示

## 测试用例4：账户锁定机制
**测试场景**: 连续3次输入错误密码
**前置条件**: 用户账户存在且未被锁定
**测试步骤**:
1. 打开登录页面
2. 输入正确的用户名和错误的密码，重复3次
**预期结果**: 账户被锁定30分钟，显示锁定提示

## 测试用例5：界面元素验证（基于图片分析）
**测试场景**: 验证登录界面包含所有必需元素
**前置条件**: 无
**测试步骤**:
1. 打开登录页面
2. 检查页面元素
**预期结果**: 
- 包含用户名输入框（浅黄色区域）
- 包含密码输入框（浅黄色区域）
- 包含登录按钮（浅绿色区域）
- 界面布局符合设计要求

APPROVE
"""
    
    print("🔄 正在生成测试用例...")
    await asyncio.sleep(1)  # 模拟生成时间
    
    print("✅ 测试用例生成完成")
    print(f"   生成用例数量: 5")
    print(f"   包含图片分析: 是")
    
    print("\n生成的测试用例预览:")
    print(mock_testcases[:500] + "...")
    
    return mock_testcases

def demo_complete_workflow():
    """演示完整的多模态工作流程"""
    print("🚀 多模态测试用例生成完整演示")
    print("=" * 50)
    
    # 步骤1：文档处理
    uploaded_text = demo_text_processing()
    
    # 步骤2：图片处理
    images = demo_image_processing()
    
    # 步骤3：多模态消息创建
    request = demo_multimodal_message()
    
    # 步骤4：测试用例生成
    if request:
        testcases = asyncio.run(demo_testcase_generation())
        
        print("\n🎉 演示完成！")
        print("=" * 50)
        print("📊 演示总结:")
        print(f"   - 处理文档: {'✅' if uploaded_text else '❌'}")
        print(f"   - 处理图片: {'✅' if images else '❌'}")
        print(f"   - 创建请求: {'✅' if request else '❌'}")
        print(f"   - 生成用例: {'✅' if 'testcases' in locals() else '❌'}")
        
        print("\n💡 实际使用提示:")
        print("1. 启动应用: streamlit run page.py")
        print("2. 上传需求文档和界面截图")
        print("3. 填写需求描述")
        print("4. 点击生成测试用例")
        print("5. AI会分析文档和图片内容生成测试用例")
        
    else:
        print("\n❌ 演示失败，请检查依赖安装")

if __name__ == "__main__":
    demo_complete_workflow()
