#!/usr/bin/python
# -*- coding: utf-8 -*-

import io
import base64
import chardet
import streamlit as st

# 可选导入，如果包不存在则跳过
try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    st.warning("PIL/Pillow 未安装，图片处理功能将受限")

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    st.warning("Pandas 未安装，Excel处理功能将受限")

try:
    from docx import Document
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False
    st.warning("python-docx 未安装，Word文档处理功能将受限")

try:
    import PyPDF2
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False
    st.warning("PyPDF2 未安装，PDF处理功能将受限")

try:
    import cv2
    import numpy as np
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False

class FileProcessor:
    """文件处理器，支持多种文件格式的解析"""

    def __init__(self):
        self.supported_text_formats = ['txt', 'md', 'csv']
        self.supported_doc_formats = ['pdf', 'docx', 'doc']
        self.supported_excel_formats = ['xlsx', 'xls', 'csv']
        self.supported_image_formats = ['jpg', 'jpeg', 'png', 'bmp', 'gif', 'tiff', 'webp']

    def get_file_type(self, file):
        """获取文件类型"""
        if hasattr(file, 'name'):
            return file.name.split('.')[-1].lower()
        return None

    def process_file(self, file):
        """处理上传的文件，返回提取的文本内容"""
        file_type = self.get_file_type(file)

        if not file_type:
            return "无法识别文件类型"

        try:
            if file_type in self.supported_text_formats:
                return self.process_text_file(file)
            elif file_type == 'pdf':
                return self.process_pdf_file(file)
            elif file_type in ['docx', 'doc']:
                return self.process_word_file(file)
            elif file_type in self.supported_excel_formats:
                return self.process_excel_file(file)
            elif file_type in self.supported_image_formats:
                return self.process_image_file(file)
            else:
                return f"不支持的文件格式: {file_type}"
        except Exception as e:
            return f"处理文件时出错: {str(e)}"

    def process_text_file(self, file):
        """处理文本文件"""
        try:
            # 尝试检测编码
            raw_data = file.read()
            encoding = chardet.detect(raw_data)['encoding'] or 'utf-8'
            file.seek(0)

            content = file.read().decode(encoding, errors='ignore')
            return content
        except Exception as e:
            return f"读取文本文件失败: {str(e)}"

    def process_pdf_file(self, file):
        """处理PDF文件"""
        if not PDF_AVAILABLE:
            return "PDF处理功能不可用：PyPDF2 未安装。请运行 'pip install PyPDF2' 安装。"

        try:
            pdf_reader = PyPDF2.PdfReader(file)
            text_content = []

            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                text_content.append(page.extract_text())

            return "\n".join(text_content)
        except Exception as e:
            return f"读取PDF文件失败: {str(e)}"

    def process_word_file(self, file):
        """处理Word文件"""
        if not DOCX_AVAILABLE:
            return "Word文档处理功能不可用：python-docx 未安装。请运行 'pip install python-docx' 安装。"

        try:
            doc = Document(file)
            text_content = []

            for paragraph in doc.paragraphs:
                text_content.append(paragraph.text)

            # 处理表格
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        row_text.append(cell.text)
                    text_content.append(" | ".join(row_text))

            return "\n".join(text_content)
        except Exception as e:
            return f"读取Word文件失败: {str(e)}"

    def process_excel_file(self, file):
        """处理Excel文件"""
        if not PANDAS_AVAILABLE:
            return "Excel处理功能不可用：pandas 未安装。请运行 'pip install pandas openpyxl' 安装。"

        try:
            # 读取所有工作表
            excel_data = pd.read_excel(file, sheet_name=None)
            text_content = []

            for sheet_name, df in excel_data.items():
                text_content.append(f"工作表: {sheet_name}")
                text_content.append(df.to_string(index=False))
                text_content.append("")

            return "\n".join(text_content)
        except Exception as e:
            return f"读取Excel文件失败: {str(e)}"

    def process_image_file(self, file):
        """处理图像文件，提取文本（OCR）"""
        if not PIL_AVAILABLE:
            return "图像处理功能不可用：PIL/Pillow 未安装。请运行 'pip install Pillow' 安装。"

        try:
            # 读取图像
            image = Image.open(file)

            # 获取图像基本信息
            width, height = image.size
            format_info = image.format
            mode = image.mode

            # 如果OpenCV可用，可以进行更多处理
            if CV2_AVAILABLE:
                # 转换为OpenCV格式（可选，用于未来的OCR功能）
                import numpy as np
                opencv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)

            image_info = f"""
图像信息:
- 尺寸: {width} x {height}
- 格式: {format_info}
- 模式: {mode}

注意: 图像内容已上传，支持多模态分析。
如需提取图像中的文字，请确保模型支持视觉功能。
"""
            return image_info

        except Exception as e:
            return f"处理图像文件失败: {str(e)}"

    def process_pasted_image(self, image_data):
        """处理粘贴的图像数据"""
        if not PIL_AVAILABLE:
            return "图像处理功能不可用：PIL/Pillow 未安装。请运行 'pip install Pillow' 安装。", None

        try:
            # 解码base64图像数据
            if image_data.startswith('data:image'):
                # 移除data:image/png;base64,前缀
                image_data = image_data.split(',')[1]

            image_bytes = base64.b64decode(image_data)
            image = Image.open(io.BytesIO(image_bytes))

            # 获取图像信息
            width, height = image.size
            format_info = image.format or "Unknown"

            image_info = f"""
粘贴的图像信息:
- 尺寸: {width} x {height}
- 格式: {format_info}

注意: 图像内容已接收，支持多模态分析。
"""
            return image_info, image

        except Exception as e:
            return f"处理粘贴图像失败: {str(e)}", None
