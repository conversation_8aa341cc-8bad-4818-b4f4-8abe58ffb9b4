#!/usr/bin/python
# -*- coding: utf-8 -*-

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.messages import TextMessage
from typing import List, Dict, Any, Optional
import asyncio

class MultimodalTestCaseWriter(AssistantAgent):
    """支持多模态输入的测试用例编写代理"""
    
    def __init__(self, model_client, system_message: str, name: str = "TestCaseWriter"):
        super().__init__(
            name=name,
            model_client=model_client,
            system_message=system_message
        )
        self._supports_vision = getattr(model_client.model_info, 'vision', False) if hasattr(model_client, 'model_info') else False
    
    async def process_multimodal_request(self, text_content: str, images: List = None, 
                                       uploaded_text: str = "") -> str:
        """
        处理多模态请求
        
        Args:
            text_content: 文本内容
            images: 图像列表
            uploaded_text: 上传文件内容
        
        Returns:
            处理结果
        """
        
        # 构建完整的消息内容
        full_content = ""
        
        if uploaded_text:
            full_content += f"【文档内容】:\n{uploaded_text}\n\n"
        
        if text_content:
            full_content += f"【需求描述】:\n{text_content}\n\n"
        
        if images and len(images) > 0:
            if self._supports_vision:
                full_content += f"【图片信息】:\n已提供 {len(images)} 张图片，请结合图片内容进行分析。\n"
                full_content += "请仔细分析图片中的界面元素、交互流程、数据结构等信息，并据此生成相应的测试用例。\n\n"
            else:
                full_content += f"【注意】:\n检测到 {len(images)} 张图片，但当前模型不支持图像分析。\n"
                full_content += "请基于文本描述生成测试用例。\n\n"
        
        # 添加测试用例生成指导
        full_content += """
【测试用例生成要求】:
1. 根据需求描述和图片内容，生成全面的测试用例
2. 测试用例应包含：功能测试、边界测试、异常测试
3. 如果有图片，请特别关注图片中展示的功能点
4. 测试用例格式要求：
   - 用例编号
   - 测试场景
   - 前置条件
   - 测试步骤
   - 预期结果
5. 生成完成后请回复"APPROVE"表示完成
"""
        
        # 创建消息
        message = TextMessage(content=full_content, source=self.name)
        
        # 发送消息并获取响应
        try:
            response = await self.on_messages([message])
            return response.content if hasattr(response, 'content') else str(response)
        except Exception as e:
            return f"处理请求时出错: {str(e)}"

def get_multimodal_testcase_writer(model_client, system_message: str) -> MultimodalTestCaseWriter:
    """
    创建多模态测试用例编写代理
    
    Args:
        model_client: 模型客户端
        system_message: 系统消息
    
    Returns:
        多模态测试用例编写代理
    """
    return MultimodalTestCaseWriter(
        model_client=model_client,
        system_message=system_message,
        name="MultimodalTestCaseWriter"
    )

class EnhancedTestCaseReviewer(AssistantAgent):
    """增强的测试用例审核代理"""
    
    def __init__(self, model_client, system_message: str, name: str = "TestCaseReviewer"):
        super().__init__(
            name=name,
            model_client=model_client,
            system_message=system_message
        )
    
    async def review_testcases(self, testcases: str, original_request: str = "") -> str:
        """
        审核测试用例
        
        Args:
            testcases: 待审核的测试用例
            original_request: 原始需求
        
        Returns:
            审核结果
        """
        
        review_content = f"""
【原始需求】:
{original_request}

【待审核测试用例】:
{testcases}

【审核要求】:
1. 检查测试用例是否完整覆盖需求
2. 验证测试步骤是否清晰可执行
3. 确认预期结果是否明确
4. 检查是否遗漏重要测试场景
5. 如果有图片相关需求，确保相关测试用例已包含
6. 审核完成后请回复"APPROVE"表示通过

请提供审核意见和改进建议。
"""
        
        message = TextMessage(content=review_content, source=self.name)
        
        try:
            response = await self.on_messages([message])
            return response.content if hasattr(response, 'content') else str(response)
        except Exception as e:
            return f"审核过程中出错: {str(e)}"

def get_enhanced_testcase_reviewer(model_client, system_message: str) -> EnhancedTestCaseReviewer:
    """
    创建增强的测试用例审核代理
    
    Args:
        model_client: 模型客户端
        system_message: 系统消息
    
    Returns:
        增强的测试用例审核代理
    """
    return EnhancedTestCaseReviewer(
        model_client=model_client,
        system_message=system_message,
        name="EnhancedTestCaseReviewer"
    )

async def run_multimodal_testcase_generation(
    writer_client, reviewer_client,
    writer_system_message: str, reviewer_system_message: str,
    text_content: str, images: List = None, uploaded_text: str = "",
    test_priority: str = "--", test_case_count: int = 0
) -> str:
    """
    运行多模态测试用例生成流程
    
    Args:
        writer_client: 编写代理的模型客户端
        reviewer_client: 审核代理的模型客户端
        writer_system_message: 编写代理系统消息
        reviewer_system_message: 审核代理系统消息
        text_content: 文本内容
        images: 图像列表
        uploaded_text: 上传文件内容
        test_priority: 测试优先级
        test_case_count: 测试用例数量
    
    Returns:
        生成的测试用例
    """
    
    try:
        # 创建代理
        writer = get_multimodal_testcase_writer(writer_client, writer_system_message)
        reviewer = get_enhanced_testcase_reviewer(reviewer_client, reviewer_system_message)
        
        # 构建完整请求
        full_request = ""
        if uploaded_text:
            full_request += f"【文档内容】:\n{uploaded_text}\n\n"
        
        full_request += f"【需求描述】:\n{text_content}\n\n"
        
        if test_priority != "--":
            full_request += f"【测试优先级】: {test_priority}\n"
        
        if test_case_count > 0:
            full_request += f"【用例数量要求】: 请严格生成 {test_case_count} 条测试用例\n"
        
        if images and len(images) > 0:
            full_request += f"【图片信息】: 已提供 {len(images)} 张图片，请结合分析\n"
        
        # 第一步：生成测试用例
        print("🔄 正在生成测试用例...")
        testcases = await writer.process_multimodal_request(
            text_content=text_content,
            images=images,
            uploaded_text=uploaded_text
        )
        
        # 第二步：审核测试用例
        print("🔍 正在审核测试用例...")
        review_result = await reviewer.review_testcases(testcases, full_request)
        
        # 返回最终结果
        final_result = f"""
{testcases}

【审核意见】:
{review_result}
"""
        
        return final_result
        
    except Exception as e:
        return f"多模态测试用例生成失败: {str(e)}"

# 测试函数
async def test_multimodal_agents():
    """测试多模态代理功能"""
    print("🧪 测试多模态代理功能...")
    
    # 这里可以添加测试代码
    print("✅ 多模态代理模块加载成功")

if __name__ == "__main__":
    asyncio.run(test_multimodal_agents())
