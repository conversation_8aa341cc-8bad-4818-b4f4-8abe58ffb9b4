#!/usr/bin/python
# -*- coding: utf-8 -*-

import subprocess
import sys
import os

def check_streamlit():
    """检查streamlit是否安装"""
    try:
        import streamlit
        return True
    except ImportError:
        return False

def install_streamlit():
    """安装streamlit"""
    print("📦 正在安装Streamlit...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "streamlit"])
        print("✅ Streamlit安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ Streamlit安装失败")
        return False

def run_app():
    """运行应用"""
    print("🚀 启动测试用例生成工具...")
    
    # 检查page.py是否存在
    if not os.path.exists("page.py"):
        print("❌ 找不到page.py文件")
        print("请确保在正确的目录中运行此脚本")
        return False
    
    try:
        # 运行streamlit应用
        subprocess.run([sys.executable, "-m", "streamlit", "run", "page.py"])
        return True
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
        return True
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 测试用例生成工具启动器")
    print("="*40)
    
    # 检查streamlit
    if not check_streamlit():
        print("⚠️ Streamlit未安装")
        if install_streamlit():
            print("✅ 准备启动应用...")
        else:
            print("❌ 无法安装Streamlit，请手动安装:")
            print("   pip install streamlit")
            return
    else:
        print("✅ Streamlit已安装")
    
    print("\n📝 提示:")
    print("- 如果遇到依赖问题，请运行: python fix_dependencies.py")
    print("- 应用将在浏览器中自动打开")
    print("- 按Ctrl+C停止应用")
    
    print("\n" + "="*40)
    
    # 运行应用
    if run_app():
        print("✅ 应用运行完成")
    else:
        print("❌ 应用运行失败")
        print("\n🔧 故障排除:")
        print("1. 检查Python版本（建议3.8+）")
        print("2. 运行: python fix_dependencies.py")
        print("3. 手动运行: streamlit run page.py")

if __name__ == "__main__":
    main()
