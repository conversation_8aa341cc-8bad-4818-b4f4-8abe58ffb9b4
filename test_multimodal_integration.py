#!/usr/bin/python
# -*- coding: utf-8 -*-

import os
import sys
import tempfile
from io import BytesIO

def test_file_processor():
    """测试文件处理器"""
    print("🧪 测试文件处理器...")
    
    try:
        from file_processor import FileProcessor
        processor = FileProcessor()
        
        # 创建测试文本
        test_text = "这是一个用户登录功能需求\n1. 用户输入用户名和密码\n2. 系统验证用户信息\n3. 登录成功跳转到主页"
        
        # 模拟文件对象
        class MockFile:
            def __init__(self, content, name):
                self.content = content.encode('utf-8')
                self.name = name
            
            def read(self):
                return self.content
            
            def seek(self, position):
                pass
        
        mock_file = MockFile(test_text, "test.txt")
        result = processor.process_text_file(mock_file)
        
        if "用户登录功能" in result:
            print("✅ 文件处理器测试通过")
            return True
        else:
            print("❌ 文件处理器测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 文件处理器测试异常: {e}")
        return False

def test_multimodal_message():
    """测试多模态消息处理"""
    print("🧪 测试多模态消息处理...")
    
    try:
        from multimodal_message import prepare_multimodal_request, create_enhanced_task_description
        
        # 测试纯文本请求
        request = prepare_multimodal_request(
            text_content="用户登录功能测试",
            test_priority="高",
            test_case_count=3
        )
        
        if request and "task" in request:
            print("✅ 多模态消息处理测试通过")
            print(f"   - 任务类型: {'多模态' if request['is_multimodal'] else '文本'}")
            print(f"   - 图片数量: {request['image_count']}")
            return True
        else:
            print("❌ 多模态消息处理测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 多模态消息处理测试异常: {e}")
        return False

def test_image_processing():
    """测试图片处理功能"""
    print("🧪 测试图片处理功能...")
    
    try:
        from PIL import Image
        from multimodal_message import image_to_base64
        
        # 创建测试图片
        test_image = Image.new('RGB', (100, 100), color='red')
        
        # 测试图片转base64
        base64_str = image_to_base64(test_image)
        
        if base64_str and len(base64_str) > 0:
            print("✅ 图片处理功能测试通过")
            print(f"   - Base64长度: {len(base64_str)}")
            return True
        else:
            print("❌ 图片处理功能测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 图片处理功能测试异常: {e}")
        return False

def test_model_configuration():
    """测试模型配置"""
    print("🧪 测试模型配置...")
    
    try:
        from llms import model_deepseek_info, model_qwen_info
        
        # 检查视觉支持
        deepseek_vision = model_deepseek_info.get("vision", False)
        qwen_vision = model_qwen_info.get("vision", False)
        
        print(f"   - DeepSeek视觉支持: {deepseek_vision}")
        print(f"   - Qwen视觉支持: {qwen_vision}")
        
        if deepseek_vision or qwen_vision:
            print("✅ 模型配置测试通过 - 支持多模态")
            return True
        else:
            print("⚠️ 模型配置测试 - 未启用视觉支持")
            return True  # 这不是错误，只是配置问题
            
    except Exception as e:
        print(f"❌ 模型配置测试异常: {e}")
        return False

def test_integration():
    """测试集成功能"""
    print("🧪 测试集成功能...")
    
    try:
        # 测试导入
        from page import MULTIMODAL_AVAILABLE, MULTIMODAL_MESSAGE_AVAILABLE, MULTIMODAL_AGENTS_AVAILABLE
        
        print(f"   - 多模态基础功能: {MULTIMODAL_AVAILABLE}")
        print(f"   - 多模态消息处理: {MULTIMODAL_MESSAGE_AVAILABLE}")
        print(f"   - 多模态代理: {MULTIMODAL_AGENTS_AVAILABLE}")
        
        if MULTIMODAL_AVAILABLE and MULTIMODAL_MESSAGE_AVAILABLE:
            print("✅ 集成功能测试通过")
            return True
        else:
            print("⚠️ 集成功能部分可用")
            return True
            
    except Exception as e:
        print(f"❌ 集成功能测试异常: {e}")
        return False

def test_streamlit_components():
    """测试Streamlit组件"""
    print("🧪 测试Streamlit组件...")
    
    try:
        import streamlit as st
        
        # 检查streamlit版本
        st_version = st.__version__
        print(f"   - Streamlit版本: {st_version}")
        
        # 测试组件导入
        try:
            from image_paste_component import create_image_paste_component
            print("   - 图片粘贴组件: 可用")
        except ImportError:
            print("   - 图片粘贴组件: 不可用")
        
        print("✅ Streamlit组件测试通过")
        return True
        
    except Exception as e:
        print(f"❌ Streamlit组件测试异常: {e}")
        return False

def main():
    """运行所有测试"""
    print("🚀 多模态集成测试")
    print("="*50)
    
    tests = [
        ("Streamlit组件", test_streamlit_components),
        ("文件处理器", test_file_processor),
        ("多模态消息", test_multimodal_message),
        ("图片处理", test_image_processing),
        ("模型配置", test_model_configuration),
        ("集成功能", test_integration),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*30}")
        print(f"🧪 {test_name}")
        print('='*30)
        
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"📊 测试总结: {passed_tests}/{total_tests} 通过")
    print('='*50)
    
    if passed_tests >= total_tests - 1:  # 允许一个测试失败
        print("🎉 多模态功能基本就绪！")
        print("\n📝 使用建议:")
        print("1. 运行 'streamlit run page.py' 启动应用")
        print("2. 上传图片文件测试多模态功能")
        print("3. 尝试粘贴截图功能")
        print("4. 检查生成的测试用例是否包含图片信息")
        
        if passed_tests < total_tests:
            print("\n⚠️ 注意事项:")
            print("- 部分功能可能受限，但基础功能可用")
            print("- 如遇问题请运行 'python fix_dependencies.py'")
    else:
        print("❌ 多模态功能存在问题，请检查依赖安装")
        print("\n🔧 修复建议:")
        print("1. 运行 'python fix_dependencies.py'")
        print("2. 检查Python版本（建议3.8+）")
        print("3. 重新安装依赖包")

if __name__ == "__main__":
    main()
