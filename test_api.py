#!/usr/bin/python
# -*- coding: utf-8 -*-

import asyncio
from autogen_ext.models.openai import OpenAIChatCompletionClient
from configparser import ConfigParser
import os

# 读取配置
conf = ConfigParser()
config_path = os.path.join(os.path.dirname(__file__), 'config.ini')
conf.read(config_path)

# 模型信息
model_info = {
    "name": "deepseek-chat",
    "parameters": {
        "max_tokens": 100,
        "temperature": 0.7,
        "top_p": 0.9,
    },
    "family": "gpt-4o",
    "functions": [],
    "vision": False,
    "json_output": True,
    "function_calling": True,
    "structured_output": True
}

async def test_api():
    try:
        print("正在测试API连接...")
        print(f"Base URL: {conf['deepseek']['base_url']}")
        print(f"Model: {conf['deepseek']['model']}")
        
        client = OpenAIChatCompletionClient(
            model=conf['deepseek']['model'],
            base_url=conf['deepseek']['base_url'],
            api_key=conf['deepseek']['api_key'],
            model_info=model_info,
        )
        
        # 简单的测试消息
        messages = [{"role": "user", "content": "你好，请回复'测试成功'"}]
        
        result = await client.create(messages=messages)
        print("API测试成功!")
        print(f"响应: {result}")
        
    except Exception as e:
        print(f"API测试失败: {str(e)}")
        print(f"错误类型: {type(e)}")

if __name__ == "__main__":
    asyncio.run(test_api())
