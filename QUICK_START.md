# 🚀 快速开始指南

## ❗ 重要：修复依赖问题

如果遇到以下错误：
```
ModuleNotFoundError: No module named 'exceptions'
```

请按以下步骤修复：

### 1. 运行修复脚本
```bash
python fix_dependencies.py
```

### 2. 或手动修复
```bash
# 卸载错误的docx包
pip uninstall docx -y

# 安装正确的包
pip install python-docx
pip install streamlit xlsxwriter Pillow pandas PyPDF2 openpyxl chardet
```

## 🎯 启动应用

### 方法1：使用启动脚本（推荐）
```bash
python start_app.py
```

### 方法2：直接启动
```bash
streamlit run page.py
```

## 📋 功能说明

### 基础功能（无需额外依赖）
- ✅ 文本输入
- ✅ TXT文件上传
- ✅ 测试用例生成

### 多模态功能（需要安装依赖）
- 📄 **文档上传**: PDF、Word、Excel
- 🖼️ **图片上传**: JPG、PNG、BMP等
- 📋 **图片粘贴**: 截图直接粘贴
- 🔍 **智能解析**: 自动提取内容

## 🔧 故障排除

### 常见问题

#### 1. 依赖包错误
```bash
# 运行修复脚本
python fix_dependencies.py

# 或手动安装
pip install --upgrade pip
pip install -r requirements.txt
```

#### 2. Streamlit未安装
```bash
pip install streamlit
```

#### 3. 多模态功能不可用
- 运行 `python fix_dependencies.py`
- 检查是否安装了正确的包

#### 4. 图片功能异常
```bash
pip install Pillow
```

#### 5. PDF处理失败
```bash
pip install PyPDF2
```

#### 6. Word文档处理失败
```bash
# 确保安装的是python-docx而不是docx
pip uninstall docx -y
pip install python-docx
```

### 检查安装状态
```bash
python test_multimodal.py
```

## 📝 使用步骤

1. **启动应用**
   ```bash
   python start_app.py
   ```

2. **配置模型**
   - 在"⚙AI模型设置"标签页配置API密钥
   - 选择要使用的模型

3. **上传内容**
   - 📄 文档上传：支持PDF、Word、Excel等
   - 🖼️ 图片上传：支持多种图片格式
   - 📋 图片粘贴：直接粘贴截图

4. **设置选项**
   - 选择用例分类占比
   - 设置测试优先级
   - 指定生成数量

5. **生成测试用例**
   - 点击"生成测试用例"按钮
   - 等待AI处理
   - 查看和下载结果

## 🌟 新功能亮点

### 多模态输入
- 同时支持文本、图片、文档输入
- 智能内容提取和分析
- 支持复杂需求文档处理

### 智能解析
- PDF文本自动提取
- Word文档表格解析
- Excel数据结构化处理
- 图片信息识别

### 用户体验
- 实时文件预览
- 拖拽上传支持
- 截图粘贴功能
- 错误提示和修复建议

## 📞 获取帮助

如果遇到问题：

1. **查看错误信息** - 应用会显示具体的错误提示
2. **运行修复脚本** - `python fix_dependencies.py`
3. **检查依赖状态** - `python test_multimodal.py`
4. **查看详细文档** - `MULTIMODAL_README.md`

## 🧪 测试多模态功能

### 验证安装
```bash
# 测试多模态集成
python test_multimodal_integration.py

# 演示多模态功能
python demo_multimodal.py
```

### 验证图片信息提取
1. **启动应用**: `python start_app.py`
2. **上传图片**: 在"🖼️ 图片上传"标签页上传截图
3. **检查提示**: 应显示"🖼️ 多模态模式：已包含 X 张图片进行分析"
4. **生成用例**: 点击"生成测试用例"，AI应分析图片内容

### 多模态功能指标
- ✅ **文档解析**: 自动提取PDF、Word、Excel内容
- ✅ **图片识别**: 支持JPG、PNG等格式
- ✅ **智能分析**: AI结合图片和文本生成测试用例
- ✅ **实时反馈**: 显示处理状态和模式信息

## 🎉 开始使用

现在您可以：
1. 运行 `python start_app.py` 启动应用
2. 在浏览器中打开显示的URL
3. 上传需求文档和界面截图
4. AI会自动分析图片内容并生成相应测试用例！

### 🔍 验证图片信息是否被正确提取

**问题症状**: 生成的测试用例没有包含图片相关内容

**解决方案**:
1. **检查模型配置**: 确保`llms.py`中`vision: True`
2. **验证图片上传**: 上传后应显示图片预览
3. **确认多模态模式**: 生成时应显示"多模态模式"提示
4. **检查生成结果**: 测试用例应包含图片分析内容

**调试步骤**:
```bash
# 1. 测试集成功能
python test_multimodal_integration.py

# 2. 查看演示效果
python demo_multimodal.py

# 3. 检查依赖
python fix_dependencies.py
```
