#!/usr/bin/python
# -*- coding: utf-8 -*-

import base64
import io
from typing import List, Dict, Any, Optional

def image_to_base64(image) -> str:
    """将PIL图像转换为base64字符串"""
    try:
        from PIL import Image
        
        # 如果是PIL图像对象
        if hasattr(image, 'save'):
            buffer = io.BytesIO()
            # 转换为RGB格式（避免RGBA等格式问题）
            if image.mode != 'RGB':
                image = image.convert('RGB')
            image.save(buffer, format='JPEG', quality=85)
            buffer.seek(0)
            image_bytes = buffer.getvalue()
            return base64.b64encode(image_bytes).decode('utf-8')
        
        # 如果是字节数据
        elif isinstance(image, bytes):
            return base64.b64encode(image).decode('utf-8')
        
        # 如果已经是base64字符串
        elif isinstance(image, str):
            return image
        
        else:
            raise ValueError(f"不支持的图像类型: {type(image)}")
            
    except Exception as e:
        print(f"图像转换失败: {e}")
        return ""

def create_multimodal_message(text_content: str, images: List = None, uploaded_text: str = "") -> List[Dict[str, Any]]:
    """
    创建多模态消息，支持文本和图像
    
    Args:
        text_content: 用户输入的文本内容
        images: 图像列表（PIL Image对象或base64字符串）
        uploaded_text: 从文件中提取的文本内容
    
    Returns:
        符合OpenAI格式的消息列表
    """
    
    # 合并所有文本内容
    full_text_content = ""
    
    if uploaded_text:
        full_text_content += f"【文档内容】:\n{uploaded_text}\n\n"
    
    if text_content:
        full_text_content += f"【需求描述】:\n{text_content}"
    
    # 如果没有图片，返回纯文本消息
    if not images or len(images) == 0:
        return [{"role": "user", "content": full_text_content}]
    
    # 创建多模态消息
    message_content = []
    
    # 添加文本部分
    if full_text_content.strip():
        message_content.append({
            "type": "text",
            "text": full_text_content
        })
    
    # 添加图片部分
    for i, image in enumerate(images):
        try:
            base64_image = image_to_base64(image)
            if base64_image:
                message_content.append({
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{base64_image}",
                        "detail": "high"  # 高质量分析
                    }
                })
                print(f"✅ 图片 {i+1} 已添加到消息中")
            else:
                print(f"⚠️ 图片 {i+1} 转换失败，跳过")
        except Exception as e:
            print(f"❌ 处理图片 {i+1} 时出错: {e}")
    
    return [{"role": "user", "content": message_content}]

def create_enhanced_task_description(text_content: str, images: List = None, uploaded_text: str = "", 
                                   test_priority: str = "--", test_case_count: int = 0) -> str:
    """
    创建增强的任务描述，包含多模态信息提示
    
    Args:
        text_content: 用户输入的文本
        images: 图像列表
        uploaded_text: 上传文件的文本内容
        test_priority: 测试优先级
        test_case_count: 测试用例数量
    
    Returns:
        完整的任务描述字符串
    """
    
    task_parts = []
    
    # 基础需求描述
    if uploaded_text:
        task_parts.append(f"【文档内容】:\n{uploaded_text}")
    
    if text_content:
        task_parts.append(f"【需求描述】:\n{text_content}")
    
    # 图片信息提示
    if images and len(images) > 0:
        task_parts.append(f"【图片信息】:\n已上传 {len(images)} 张图片，请结合图片内容进行分析。")
        task_parts.append("请仔细分析图片中的界面元素、交互流程、数据结构等信息，并据此生成相应的测试用例。")
    
    # 测试要求
    requirements = []
    if test_priority != "--":
        requirements.append(f"测试优先级: {test_priority}")
    
    if test_case_count > 0:
        requirements.append(f"【重要】请严格生成 {test_case_count} 条测试用例，不多不少。")
    
    if requirements:
        task_parts.append("【测试要求】:\n" + "\n".join(requirements))
    
    # 多模态分析指导
    if images and len(images) > 0:
        task_parts.append("""
【多模态分析指导】:
1. 如果图片包含界面截图，请分析界面元素、布局、交互方式
2. 如果图片包含流程图，请分析业务流程和逻辑关系
3. 如果图片包含数据表格，请分析数据结构和字段关系
4. 结合图片信息和文本描述，生成全面的测试用例
5. 确保测试用例覆盖图片中展示的所有关键功能点
""")
    
    return "\n\n".join(task_parts)

def validate_multimodal_support(model_info: Dict[str, Any]) -> bool:
    """
    验证模型是否支持多模态输入
    
    Args:
        model_info: 模型信息字典
    
    Returns:
        是否支持多模态
    """
    return model_info.get("vision", False)

def prepare_multimodal_request(text_content: str, images: List = None, uploaded_text: str = "",
                             test_priority: str = "--", test_case_count: int = 0,
                             model_info: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    准备多模态请求数据
    
    Args:
        text_content: 文本内容
        images: 图像列表
        uploaded_text: 上传文件内容
        test_priority: 测试优先级
        test_case_count: 测试用例数量
        model_info: 模型信息
    
    Returns:
        请求数据字典
    """
    
    # 检查模型是否支持多模态
    supports_vision = validate_multimodal_support(model_info or {})
    
    if images and len(images) > 0 and not supports_vision:
        print("⚠️ 当前模型不支持图像输入，将仅使用文本内容")
        images = None
    
    # 创建任务描述
    if supports_vision and images:
        # 支持多模态的情况下，创建结构化消息
        task = create_enhanced_task_description(
            text_content, images, uploaded_text, test_priority, test_case_count
        )
        messages = create_multimodal_message(text_content, images, uploaded_text)
        
        return {
            "task": task,
            "messages": messages,
            "is_multimodal": True,
            "image_count": len(images) if images else 0
        }
    else:
        # 纯文本模式
        task = create_enhanced_task_description(
            text_content, None, uploaded_text, test_priority, test_case_count
        )
        
        return {
            "task": task,
            "messages": [{"role": "user", "content": task}],
            "is_multimodal": False,
            "image_count": 0
        }

# 测试函数
def test_multimodal_message():
    """测试多模态消息创建功能"""
    
    # 测试纯文本
    text_msg = create_multimodal_message("这是一个登录功能需求")
    print("纯文本消息:", text_msg)
    
    # 测试增强任务描述
    task_desc = create_enhanced_task_description(
        "用户登录功能",
        images=["fake_image"],
        test_priority="高",
        test_case_count=5
    )
    print("任务描述:", task_desc)

if __name__ == "__main__":
    test_multimodal_message()
