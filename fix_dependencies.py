#!/usr/bin/python
# -*- coding: utf-8 -*-

import subprocess
import sys
import os

def run_command(command):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def uninstall_wrong_docx():
    """卸载错误的docx包"""
    print("🔍 检查是否安装了错误的docx包...")
    
    # 检查是否安装了错误的docx包
    success, stdout, stderr = run_command("pip list | findstr docx")
    
    if "docx " in stdout:  # 注意空格，避免匹配python-docx
        print("❌ 发现错误的docx包，正在卸载...")
        success, stdout, stderr = run_command("pip uninstall docx -y")
        if success:
            print("✅ 成功卸载错误的docx包")
        else:
            print(f"❌ 卸载失败: {stderr}")
            return False
    else:
        print("✅ 未发现错误的docx包")
    
    return True

def install_correct_packages():
    """安装正确的包"""
    print("📦 安装必需的依赖包...")
    
    # 基础必需包
    essential_packages = [
        "streamlit",
        "xlsxwriter", 
        "Pillow",
        "pandas",
        "python-docx",  # 正确的Word文档处理包
        "PyPDF2",
        "openpyxl",
        "chardet"
    ]
    
    success_count = 0
    
    for package in essential_packages:
        print(f"📥 安装 {package}...")
        success, stdout, stderr = run_command(f"pip install {package}")
        
        if success:
            print(f"✅ {package} 安装成功")
            success_count += 1
        else:
            print(f"❌ {package} 安装失败: {stderr}")
    
    print(f"\n📊 安装结果: {success_count}/{len(essential_packages)} 个包安装成功")
    return success_count == len(essential_packages)

def install_optional_packages():
    """安装可选包"""
    print("\n📦 安装可选依赖包...")
    
    optional_packages = [
        "opencv-python",
        "autogen-agentchat",
        "autogen-ext", 
        "autogen-core"
    ]
    
    success_count = 0
    
    for package in optional_packages:
        print(f"📥 尝试安装 {package}...")
        success, stdout, stderr = run_command(f"pip install {package}")
        
        if success:
            print(f"✅ {package} 安装成功")
            success_count += 1
        else:
            print(f"⚠️ {package} 安装失败（可选包）: {stderr}")
    
    print(f"\n📊 可选包安装结果: {success_count}/{len(optional_packages)} 个包安装成功")

def test_imports():
    """测试导入"""
    print("\n🧪 测试包导入...")
    
    test_packages = [
        ("streamlit", "Streamlit"),
        ("PIL", "Pillow"),
        ("pandas", "Pandas"),
        ("docx", "python-docx"),
        ("PyPDF2", "PyPDF2"),
        ("xlsxwriter", "XlsxWriter"),
        ("openpyxl", "OpenPyXL"),
        ("chardet", "Chardet")
    ]
    
    success_count = 0
    
    for package, name in test_packages:
        try:
            __import__(package)
            print(f"✅ {name}: 导入成功")
            success_count += 1
        except ImportError as e:
            print(f"❌ {name}: 导入失败 - {e}")
        except Exception as e:
            print(f"⚠️ {name}: 导入异常 - {e}")
    
    print(f"\n📊 导入测试结果: {success_count}/{len(test_packages)} 个包导入成功")
    return success_count >= 6  # 至少6个基础包成功

def main():
    """主函数"""
    print("🚀 修复依赖包问题...")
    print("="*50)
    
    # 步骤1: 卸载错误的docx包
    if not uninstall_wrong_docx():
        print("❌ 卸载错误包失败，请手动执行: pip uninstall docx -y")
        return
    
    print("\n" + "="*50)
    
    # 步骤2: 安装正确的包
    if not install_correct_packages():
        print("❌ 部分必需包安装失败")
    
    print("\n" + "="*50)
    
    # 步骤3: 安装可选包
    install_optional_packages()
    
    print("\n" + "="*50)
    
    # 步骤4: 测试导入
    if test_imports():
        print("\n🎉 依赖修复完成！现在可以运行应用了。")
        print("\n📝 下一步:")
        print("1. 运行: streamlit run page.py")
        print("2. 在浏览器中打开显示的URL")
        print("3. 测试多模态文件上传功能")
    else:
        print("\n⚠️ 部分包仍有问题，但基础功能应该可用。")
        print("\n🔧 如果仍有问题，请尝试:")
        print("1. 重启Python环境")
        print("2. 更新pip: python -m pip install --upgrade pip")
        print("3. 使用虚拟环境")

if __name__ == "__main__":
    main()
