# AutoGenTestCase
借助AI大模型帮助生成测试用例

### 打包程序下载地址
https://pan.baidu.com/s/1Cftl4BiWh_reU-oCwW3aMg  提取码：6bwu

### 模型收费情况：
#### DeepSeek申请apikey后需要充值金额才能使用，收费标准比较低2元可以使用上百次不止，放心大胆使用
#### 通义千问申请apikey后会先赠送百万tokens（够用），收费标准也比较低，放心使用

### 申请DeepSeek的apikey
1、链接：https://platform.deepseek.com/api_keys<br>
2、注册账号并登录<br>
3、创建apikey<br>
![image](https://github.com/user-attachments/assets/28310179-7263-4abc-a3e6-6e5599808fe5)


### 申请通义千问的apikey
1、链接：https://bailian.console.aliyun.com/?tab=model#/api-key<br>
2、注册账号并登录（可直接用支付宝账号登录）<br>
3、创建apikey<br>
![image](https://github.com/user-attachments/assets/9e42f4c5-d4c6-4baf-b18e-dc184bb9a507)
